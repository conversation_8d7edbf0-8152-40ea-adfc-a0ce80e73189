#!/usr/bin/env python3
"""
测试动态字段功能
"""

import requests
import json
import time

# API基础URL
BASE_URL = "http://copilot.csvw.com/rag_service/api/v1"

def test_upload_with_dynamic_fields():
    """测试上传包含动态字段的数据"""
    print("=== 测试上传动态字段数据 ===")
    
    url = f"{BASE_URL}/upload_texts"
    data = {
        "texts": [
            "这是一辆红色的轿车，配备了先进的安全系统",
            "这是一辆蓝色的SUV，适合家庭出行",
            "这是一辆绿色的电动车，环保节能"
        ],
        "collection": "test_cars_dynamic",
        "database": "test_db",
        "car_type": "passenger",      # 动态字段
        "model_year": 2024,           # 动态字段
        "brand": "TestBrand",         # 动态字段
        "is_electric": True,          # 动态字段
        "metadata": {
            "category": "automotive",
            "source": "test_data"
        }
    }
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"成功上传: {result}")
        return result.get('doc_id')
    else:
        print(f"上传失败: {response.json()}")
        return None

def test_search_dynamic_fields(doc_id=None):
    """测试搜索动态字段数据"""
    print("\n=== 测试搜索动态字段数据 ===")
    
    # 等待一下确保数据已经索引
    time.sleep(2)
    
    url = f"{BASE_URL}/search"
    data = {
        "text": "红色轿车",
        "collection": "test_cars_dynamic",
        "database": "test_db",
        "top_k": 5
    }
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"查询: {result.get('query')}")
        print(f"结果数量: {result.get('total_results')}")
        
        for i, res in enumerate(result.get('results', []), 1):
            print(f"\n结果 {i}:")
            print(f"  内容: {res.get('content')}")
            print(f"  相似度: {res.get('similarity'):.4f}")
            print(f"  ID: {res.get('id')}")
            print(f"  car_type: {res.get('car_type', '未找到')}")
            print(f"  model_year: {res.get('model_year', '未找到')}")
            print(f"  brand: {res.get('brand', '未找到')}")
            print(f"  is_electric: {res.get('is_electric', '未找到')}")
            
            # 显示metadata
            metadata = res.get('metadata', {})
            if metadata:
                print(f"  metadata: {metadata}")
    else:
        print(f"搜索失败: {response.json()}")

def test_search_with_filter():
    """测试使用动态字段进行过滤搜索"""
    print("\n=== 测试动态字段过滤搜索 ===")
    
    url = f"{BASE_URL}/search"
    data = {
        "text": "汽车",
        "collection": "test_cars_dynamic", 
        "database": "test_db",
        "top_k": 5,
        "filter": "car_type == 'passenger'"  # 使用动态字段过滤
    }
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"过滤条件: car_type == 'passenger'")
        print(f"结果数量: {result.get('total_results')}")
        
        for i, res in enumerate(result.get('results', []), 1):
            print(f"\n过滤结果 {i}:")
            print(f"  内容: {res.get('content')}")
            print(f"  car_type: {res.get('car_type', '未找到')}")
            print(f"  model_year: {res.get('model_year', '未找到')}")
    else:
        print(f"过滤搜索失败: {response.json()}")

def test_upload_different_dynamic_fields():
    """测试上传不同动态字段的数据"""
    print("\n=== 测试不同动态字段 ===")
    
    url = f"{BASE_URL}/upload_texts"
    data = {
        "texts": [
            "这是一份技术文档，介绍了AI的应用",
            "这是一份产品说明书，详细描述了功能特性"
        ],
        "collection": "test_docs_dynamic",
        "database": "test_db",
        "document_type": "technical",    # 不同的动态字段
        "department": "engineering",     # 不同的动态字段
        "priority": "high",              # 不同的动态字段
        "version": "1.0",                # 不同的动态字段
        "metadata": {
            "category": "documentation",
            "language": "chinese"
        }
    }
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        print(f"成功上传不同动态字段的数据: {response.json()}")
    else:
        print(f"上传失败: {response.json()}")

if __name__ == "__main__":
    print("开始测试动态字段功能...\n")
    
    try:
        # 测试上传动态字段数据
        doc_id = test_upload_with_dynamic_fields()
        
        # 测试搜索动态字段数据
        test_search_dynamic_fields(doc_id)
        
        # 测试动态字段过滤
        test_search_with_filter()
        
        # 测试不同的动态字段
        test_upload_different_dynamic_fields()
        
        print("\n所有动态字段测试完成！")
        
    except requests.exceptions.ConnectionError:
        print("错误: 无法连接到API服务器")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
