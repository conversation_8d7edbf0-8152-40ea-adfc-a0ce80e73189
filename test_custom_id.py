#!/usr/bin/env python3
"""
测试自定义ID功能的示例脚本
"""

import requests
import json

# API基础URL
BASE_URL = "http://copilot.csvw.com/rag_service/api/v1/upload_texts"

def test_upload_with_auto_id():
    """测试使用自增ID上传"""
    print("=== 测试自增ID上传 ===")
    
    url = f"{BASE_URL}/upload_texts"
    data = {
        "texts": [
            "这是第一个测试文本",
            "这是第二个测试文本",
            "这是第三个测试文本"
        ],
        "collection": "test_auto_id",
        "database": "test_db"
    }
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    print()

def test_upload_with_custom_id():
    """测试使用自定义ID上传"""
    print("=== 测试自定义ID上传 ===")
    
    url = f"{BASE_URL}/upload_texts"
    data = {
        "texts": [
            "这是第一个自定义ID文本",
            "这是第二个自定义ID文本", 
            "这是第三个自定义ID文本"
        ],
        "ids": [1001, 1002, 1003],  # 自定义ID列表
        "collection": "test_custom_id",
        "database": "test_db"
    }
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    print()

def test_upload_with_string_id():
    """测试使用字符串ID上传"""
    print("=== 测试字符串ID上传 ===")
    
    url = f"{BASE_URL}/upload_texts"
    data = {
        "texts": [
            "这是第一个字符串ID文本",
            "这是第二个字符串ID文本"
        ],
        "ids": ["doc_001", "doc_002"],  # 字符串ID列表
        "collection": "test_string_id",
        "database": "test_db"
    }
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    print()

def test_upload_with_mixed_fields():
    """测试同时使用自定义ID和动态字段"""
    print("=== 测试自定义ID + 动态字段 ===")
    
    url = f"{BASE_URL}/upload_texts"
    data = {
        "texts": [
            "汽车相关文档1",
            "汽车相关文档2"
        ],
        "ids": [2001, 2002],
        "collection": "test_mixed",
        "database": "test_db",
        "car_type": "passenger",  # 动态字段
        "model_year": 2023,       # 动态字段
        "metadata": {
            "category": "automotive",
            "source": "manual"
        }
    }
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    print()

def test_search_custom_id():
    """测试搜索自定义ID的数据"""
    print("=== 测试搜索自定义ID数据 ===")
    
    url = f"{BASE_URL}/search"
    data = {
        "text": "汽车文档",
        "collection": "test_mixed",
        "database": "test_db",
        "top_k": 5
    }
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    result = response.json()
    print(f"查询: {result.get('query')}")
    print(f"结果数量: {result.get('total_results')}")
    
    for i, res in enumerate(result.get('results', []), 1):
        print(f"结果 {i}:")
        print(f"  内容: {res.get('content')}")
        print(f"  相似度: {res.get('similarity')}")
        print(f"  ID: {res.get('id', '未知')}")
        print(f"  car_type: {res.get('car_type', '未知')}")
        print()

def test_error_cases():
    """测试错误情况"""
    print("=== 测试错误情况 ===")
    
    # 测试ID列表长度不匹配
    url = f"{BASE_URL}/upload_texts"
    data = {
        "texts": ["文本1", "文本2", "文本3"],
        "ids": [1, 2],  # 长度不匹配
        "collection": "test_error"
    }
    
    response = requests.post(url, json=data)
    print(f"ID长度不匹配 - 状态码: {response.status_code}")
    print(f"错误信息: {response.json()}")
    print()

if __name__ == "__main__":
    print("开始测试自定义ID功能...\n")
    
    try:
        # 测试各种场景
        test_upload_with_auto_id()
        test_upload_with_custom_id()
        test_upload_with_string_id()
        test_upload_with_mixed_fields()
        test_search_custom_id()
        test_error_cases()
        
        print("所有测试完成！")
        
    except requests.exceptions.ConnectionError:
        print("错误: 无法连接到API服务器，请确保服务器正在运行在 http://localhost:8000")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
