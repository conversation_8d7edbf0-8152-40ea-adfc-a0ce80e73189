#!/usr/bin/env python3
"""
简单测试自定义ID功能
"""

import requests
import json

# API基础URL
BASE_URL = "http://copilot.csvw.com/rag_service/api/v1"

def test_auto_id():
    """测试自增ID"""
    print("=== 测试自增ID ===")
    
    url = f"{BASE_URL}/upload_texts"
    data = {
        "texts": ["自增ID测试文本1", "自增ID测试文本2"],
        "collection": "test_auto_id_v2",
        "database": "test_db"
    }
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        print(f"成功: {response.json()}")
    else:
        print(f"失败: {response.json()}")
    print()

def test_dynamic_fields():
    """测试动态字段功能"""
    print("=== 测试动态字段 ===")

    url = f"{BASE_URL}/upload_texts"
    data = {
        "texts": ["汽车相关文档1", "汽车相关文档2"],
        "collection": "test_dynamic_fields",
        "database": "test_db",
        "car_type": "passenger",  # 动态字段
        "model_year": 2023,       # 动态字段
        "metadata": {
            "category": "automotive",
            "source": "manual"
        }
    }

    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        print(f"成功: {response.json()}")
    else:
        print(f"失败: {response.json()}")
    print()

def test_search():
    """测试搜索动态字段数据"""
    print("=== 测试搜索动态字段数据 ===")

    url = f"{BASE_URL}/search"
    data = {
        "text": "汽车文档",
        "collection": "test_dynamic_fields",
        "database": "test_db",
        "top_k": 5
    }
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"查询: {result.get('query')}")
        print(f"结果数量: {result.get('total_results')}")
        
        for i, res in enumerate(result.get('results', []), 1):
            print(f"结果 {i}:")
            print(f"  内容: {res.get('content')}")
            print(f"  相似度: {res.get('similarity')}")
            print(f"  ID: {res.get('id')}")
            print(f"  car_type: {res.get('car_type', '未知')}")
            print(f"  model_year: {res.get('model_year', '未知')}")
    else:
        print(f"失败: {response.json()}")
    print()

if __name__ == "__main__":
    print("开始功能测试...\n")

    try:
        test_auto_id()
        test_dynamic_fields()
        test_search()

        print("测试完成！")
        
    except requests.exceptions.ConnectionError:
        print("错误: 无法连接到API服务器")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
