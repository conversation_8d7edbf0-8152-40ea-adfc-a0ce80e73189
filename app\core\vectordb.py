from abc import ABC, abstractmethod
from typing import List, Dict, Any, Tuple, Optional, Union
import time
import asyncio
from datetime import datetime

class VectorDatabase(ABC):
    @abstractmethod
    async def connect(self):
        pass

    @abstractmethod
    async def create_collection(self, name: str, dim: int):
        pass

    @abstractmethod
    async def insert_vectors(self, collection: str, records: List[Dict[str, Any]]):
        pass

    @abstractmethod
    async def search_vectors(self, collection: str, vector: List[float], top_k: int, filter_expr: str = "") -> List[Dict]:
        pass

    @abstractmethod
    async def upsert_vectors(self, collection: str, records: List[Dict[str, Any]]):
        """更新或插入向量记录"""
        pass

    @abstractmethod
    async def delete_vectors(self, collection: str, ids: List[Any] = None, filter_expr: str = ""):
        """删除向量记录，可以按ID或按条件删除"""
        pass

    @abstractmethod
    async def flush_collection(self, collection: str):
        """刷新集合数据，确保数据持久化"""
        pass

# 全局连接池
_connection_pool: Dict[str, Tuple[Any, float]] = {}  # {key: (client, last_used_time)}
_pool_lock = asyncio.Lock()  # 防止并发问题
_pool_max_size = 3  # 最大连接数（根据您的需求设置为3）
_pool_health_check_interval = 60  # 健康检查间隔（秒）
_last_health_check = time.time()  # 上次健康检查时间

class MilvusVectorDB(VectorDatabase):
    def __init__(self, uri: str, token: str = None, database: str = None):
        # milvus_client连接方式参数
        self.uri = uri
        self.token = token
        # 数据库名称
        self.database = database
        self.client = None
        self.collections_cache = set()

    @classmethod
    async def _check_connection_health(cls, client):
        """检查连接是否健康"""
        try:
            # 执行一个简单的操作来验证连接
            client.list_collections()
            return True
        except Exception as e:
            print(f"[连接池] 连接健康检查失败: {e}")
            return False

    @classmethod
    async def _perform_pool_health_check(cls):
        """执行连接池健康检查"""
        global _last_health_check
        current_time = time.time()

        # 检查是否需要进行健康检查
        if current_time - _last_health_check < _pool_health_check_interval:
            return

        _last_health_check = current_time
        print(f"[连接池] 开始执行连接池健康检查: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        async with _pool_lock:
            # 检查所有连接的健康状态
            unhealthy_keys = []
            for key, (client, _) in _connection_pool.items():
                if not await cls._check_connection_health(client):
                    unhealthy_keys.append(key)

            # 移除不健康的连接
            for key in unhealthy_keys:
                print(f"[连接池] 移除不健康的连接: {key}")
                del _connection_pool[key]

        print(f"[连接池] 健康检查完成，当前连接池大小: {len(_connection_pool)}")

    @classmethod
    async def get_connection(cls, uri: str, token: str = None, database: str = None):
        """从连接池获取连接，如果不存在或无效则创建新连接"""
        # 执行连接池健康检查
        await cls._perform_pool_health_check()

        pool_key = f"{uri}:{database or 'default'}"

        async with _pool_lock:  # 防止并发问题
            # 尝试从池中获取连接
            if pool_key in _connection_pool:
                client, last_used_time = _connection_pool[pool_key]

                # 验证连接是否有效
                if await cls._check_connection_health(client):
                    # 更新最后使用时间
                    _connection_pool[pool_key] = (client, time.time())
                    print(f"[连接池] 使用现有连接: {pool_key}")
                    return client
                else:
                    print(f"[连接池] 连接已失效，将创建新连接: {pool_key}")
                    # 连接无效，从池中移除
                    del _connection_pool[pool_key]

            # 检查连接池大小
            if len(_connection_pool) >= _pool_max_size:
                # 移除最旧的连接
                oldest_key = min(_connection_pool.items(), key=lambda x: x[1][1])[0]
                del _connection_pool[oldest_key]
                print(f"[连接池] 连接池已满，移除最旧连接: {oldest_key}")

            # 创建新连接
            print(f"[连接池] 创建新连接: {pool_key}")
            from pymilvus import MilvusClient

            try:
                if database and len(database.strip()) > 0:
                    client = MilvusClient(
                        uri=uri,
                        token=token if token and len(token.strip()) > 0 else None,
                        db_name=database,
                        # 设置较长的超时时间，避免连接断开
                        timeout=300  # 5分钟超时
                    )
                else:
                    client = MilvusClient(
                        uri=uri,
                        token=token if token and len(token.strip()) > 0 else None,
                        timeout=300  # 5分钟超时
                    )

                # 将新连接添加到连接池
                _connection_pool[pool_key] = (client, time.time())
                print(f"[连接池] 新连接创建成功: {pool_key}")
                return client
            except Exception as e:
                print(f"[连接池] 创建连接失败: {e}")
                raise

    async def connect(self):
        """连接到Milvus数据库，使用连接池获取连接"""
        connect_start = time.time()
        try:
            print(f"[连接] 连接到Milvus，URI: {self.uri}")
            if self.database:
                print(f"[连接] 使用数据库: {self.database}")
            else:
                print("[连接] 使用默认数据库")

            # 使用连接池获取客户端
            self.client = await self.get_connection(
                uri=self.uri,
                token=self.token,
                database=self.database
            )

            connect_time = time.time() - connect_start
            print(f"[连接] 成功连接到Milvus，耗时: {connect_time:.3f}秒")
        except Exception as e:
            print(f"[错误] 连接Milvus失败: {e}")
            raise

        # 初始化后获取所有集合名称
        await self._refresh_collections()

    async def _refresh_collections(self):
        """刷新集合列表缓存"""
        try:
            # 使用client方式获取集合列表
            collections = self.client.list_collections()
            self.collections_cache = set(collections)
            print(f"Available collections: {self.collections_cache}")
        except Exception as e:
            print(f"Error refreshing collections: {e}")
            self.collections_cache = set()

    async def create_database(self, db_name: str):
        """创建新的数据库"""
        try:
            # 检查客户端是否初始化
            if self.client is None:
                raise ValueError("Milvus client is not initialized. Connection may have failed.")

            # 获取现有数据库列表
            try:
                databases = self.client.list_databases()
                print(f"Available databases: {databases}")

                # 检查数据库是否已存在
                if db_name in databases:
                    print(f"Database '{db_name}' already exists")
                    return db_name
            except Exception as e:
                print(f"Warning: Unable to list databases: {e}")
                # 继续尝试创建数据库

            # 创建新数据库
            try:
                self.client.create_database(db_name=db_name)
                print(f"Created database '{db_name}'")
                return db_name
            except Exception as e:
                # 如果创建失败，检查是否是因为数据库已存在
                if "already exists" in str(e).lower():
                    print(f"Database '{db_name}' already exists")
                    return db_name
                else:
                    print(f"Error creating database: {e}")
                    raise
        except Exception as e:
            print(f"Error creating database: {e}")
            raise

    async def create_collection(self, name: str, dim: int):
        """创建集合，如果已存在则加载"""
        # 确保已连接到数据库
        if self.client is None:
            print("Database connection not initialized, connecting now...")
            await self.connect()

        # 使用client方式创建集合
        try:
            # 检查集合是否已存在
            if self.client is None:
                raise ValueError("Milvus client is not initialized. Connection may have failed.")

            collections = self.client.list_collections()
            if name in collections:
                print(f"Collection '{name}' already exists")
                return name

            # 创建新集合，使用明确的schema定义JSON字段
            try:
                from pymilvus import DataType

                # 创建schema
                schema = self.client.create_schema(
                    auto_id=auto_id,
                    enable_dynamic_field=True,
                    description="Text embedding collection with JSON metadata"
                )

                # 添加字段
                schema.add_field(field_name="id", datatype=DataType.INT64, is_primary=True, auto_id=auto_id)
                schema.add_field(field_name="vector", datatype=DataType.FLOAT_VECTOR, dim=dim)
                schema.add_field(field_name="content", datatype=DataType.VARCHAR, max_length=65535)
                schema.add_field(field_name="collection_name", datatype=DataType.VARCHAR, max_length=255)
                schema.add_field(field_name="metadata", datatype=DataType.JSON)  # 使用原生JSON字段

                # 创建索引参数
                index_params = self.client.prepare_index_params()

                # 为向量字段创建索引
                index_params.add_index(
                    field_name="vector",
                    index_type="AUTOINDEX",
                    metric_type="COSINE"
                )

                # 可选：为JSON字段的常用路径创建索引以提升查询性能
                # 这里可以根据实际使用情况添加特定路径的索引
                # 例如：为metadata中的category字段创建索引
                # index_params.add_index(
                #     field_name="metadata",
                #     index_type="INVERTED",
                #     index_name="metadata_category_index",
                #     params={
                #         "json_path": "metadata[\"category\"]",
                #         "json_cast_type": "varchar"
                #     }
                # )

                # 创建集合
                self.client.create_collection(
                    collection_name=name,
                    schema=schema,
                    index_params=index_params
                )
                print(f"Created collection '{name}' with JSON metadata field")

            except Exception as e:
                print(f"Error creating collection: {e}")
                raise

            # 加载集合
            try:
                # 尝试使用load_collection方法
                self.client.load_collection(collection_name=name)
                print(f"Loaded collection '{name}'")
            except AttributeError as e:
                print(f"Warning: load_collection method not available: {e}")
                # 在某些版本的MilvusClient中，创建集合后会自动加载，不需要显式调用load_collection

            # 更新集合缓存
            self.collections_cache.add(name)
            print(f"Created collection '{name}'")
            return name
        except Exception as e:
            print(f"Error creating collection: {e}")
            raise

    async def insert_vectors(self, collection: str, records: List[Dict[str, Any]]):
        """插入向量到指定集合"""
        if not records:
            print(f"No records to insert into collection '{collection}'")
            return 0

        # 确保集合存在
        if collection not in self.collections_cache:
            if 'vector' in records[0]:
                dim = len(records[0]['vector'])
                await self.create_collection(collection, dim)
            else:
                raise ValueError(f"Collection {collection} does not exist and no vectors provided")

        # 使用client方式插入向量
        try:
            # 准备数据
            vectors = [r['vector'] for r in records]

            # 将其他字段整合到一个字典中作为属性
            entities = []
            for r in records:
                # 处理metadata字段，支持JSON对象和字符串格式
                metadata_obj = r.get('metadata', {})

                # 向后兼容：如果传入的是字符串，尝试解析为JSON
                if isinstance(metadata_obj, str):
                    try:
                        import json
                        metadata_obj = json.loads(metadata_obj)
                        print(f"[兼容性] 成功解析字符串格式的metadata")
                    except Exception as e:
                        print(f"[兼容性警告] 无法解析metadata字符串，使用空字典: {e}")
                        metadata_obj = {}
                elif not isinstance(metadata_obj, dict):
                    print(f"[警告] metadata不是字典或字符串类型，已重置为空字典")
                    metadata_obj = {}

                # 创建基本实体
                entity = {
                    "vector": r['vector'],
                    "content": r.get('content', ''),
                    "collection_name": collection,
                    "metadata": metadata_obj  # 直接使用JSON对象
                }

                # 如果记录中包含id字段，则添加到entity中（用于自定义ID）
                if 'id' in r:
                    entity['id'] = r['id']
                    print(f"[ID处理] 使用自定义ID: {r['id']}")
                else:
                    print(f"[ID处理] 使用自增ID")

                # 添加所有额外字段作为独立列
                for key, value in r.items():
                    # 跳过已处理的标准字段
                    if key in ['vector', 'content', 'metadata']:
                        continue

                    # 如果是自定义字段，直接添加到entity中
                    # 注意：Milvus会自动处理动态字段
                    entity[key] = value
                    print(f"[动态字段] 添加字段 '{key}' = {value} 作为独立列")

                entities.append(entity)

            # 插入数据
            self.client.insert(
                collection_name=collection,
                data=entities
            )
            print(f"Inserted {len(vectors)} vectors into collection '{collection}' using JSON metadata")
            return len(vectors)
        except Exception as e:
            print(f"Error inserting vectors: {e}")
            raise

    async def search_vectors(self, collection: str, vector: List[float], top_k: int, filter_expr: str = "") -> List[Dict]:
        """在指定集合中搜索向量，如果collection='all'则搜索所有集合

        Args:
            collection: 集合名称，如果为'all'则搜索所有集合
            vector: 查询向量
            top_k: 返回结果数量
            filter_expr: 筛选表达式，例如 "car_type='passenger'"
        """
        import time
        search_start = time.time()
        print(f"[向量库] 开始搜索向量, 集合: {collection}, top_k: {top_k}")


        # 确保连接有效
        if self.client is None:
            print("[向量库] 客户端未初始化，尝试连接...")
            await self.connect()

        # 使用client方式搜索
        try:
            # 确定要搜索的集合
            collections_to_search = []
            if collection.lower() == 'all':

                # 只有在搜索所有集合或需要验证集合存在时才刷新集合列表
                refresh_start = time.time()
                await self._refresh_collections()
                refresh_time = time.time() - refresh_start
                print(f"[向量库时间] 刷新集合列表: {refresh_time:.3f}秒")

                # 搜索所有集合
                collections_to_search = list(self.collections_cache)
                if not collections_to_search:
                    print("No collections available to search")
                    return []
            else:
                # 搜索指定集合
                if collection not in self.collections_cache:
                    print(f"Collection '{collection}' does not exist")
                    return []
                collections_to_search = [collection]

            # 搜索参数 (MilvusClient不需要显式指定搜索参数)

            # 打印筛选表达式
            if filter_expr:
                print(f"[搜索] 使用筛选表达式: {filter_expr}")

            all_results = []
            for coll_name in collections_to_search:
                try:
                    # 执行搜索，使用余弦相似度
                    # 获取集合的所有字段
                    # 创建搜索参数，使用通配符获取所有字段
                    search_args = {
                        "collection_name": coll_name,
                        "data": [vector],
                        "limit": top_k,
                        "output_fields": ["*"],  # 使用通配符获取所有字段
                        "search_params": {"metric_type": "COSINE"}
                    }

                    # 如果有筛选表达式，添加到搜索参数中
                    if filter_expr:
                        search_args["filter"] = filter_expr

                    # 执行搜索
                    search_exec_start = time.time()
                    print(f"[向量库时间] 开始执行搜索: {time.strftime('%H:%M:%S.%f')[:-3]}")
                    results = self.client.search(**search_args)
                    search_exec_time = time.time() - search_exec_start
                    print(f"[向量库时间] 搜索执行完成: {search_exec_time:.3f}秒")

                    # 处理结果
                    process_start = time.time()
                    print(f"[向量库时间] 开始处理结果: {time.strftime('%H:%M:%S.%f')[:-3]}")
                    for hits in results:
                        for hit in hits:
                            entity = hit['entity']

                            # 处理metadata字段，兼容新旧两种格式
                            metadata = entity.get('metadata', {})

                            # 向后兼容：处理旧的字符串格式metadata
                            if isinstance(metadata, str):
                                try:
                                    import json
                                    metadata = json.loads(metadata)
                                    print(f"[兼容性] 成功解析旧格式的字符串metadata")
                                except Exception as e:
                                    print(f"[兼容性警告] 无法解析旧格式metadata字符串: {e}")
                                    metadata = {}
                            elif not isinstance(metadata, dict):
                                print(f"[警告] metadata不是字典或字符串类型，已重置为空字典")
                                metadata = {}

                            # 创建基本结果字典
                            result = {
                                'content': entity.get('content', ''),
                                'collection': entity.get('collection_name', coll_name),
                                'metadata': metadata,
                                'distance': float(hit.get('distance', 0))
                            }

                            # 添加所有额外字段到结果中
                            for key, value in entity.items():
                                # 跳过已处理的标准字段和vector字段
                                if key in ['content', 'collection_name', 'metadata', 'vector']:
                                    continue

                                # 将额外字段添加到结果中
                                result[key] = value

                            all_results.append(result)
                except Exception as e:
                    print(f"Error searching collection '{coll_name}': {e}")

            # 处理结果完成
            if 'process_start' in locals():
                process_time = time.time() - process_start
                print(f"[向量库时间] 结果处理完成: {process_time:.3f}秒")

            # 按相似度排序并限制结果数量
            sort_start = time.time()
            # 使用余弦相似度时，值越大表示越相似，所以需要降序排序
            all_results.sort(key=lambda x: x['distance'], reverse=True)
            limited_results = all_results[:top_k]
            sort_time = time.time() - sort_start
            print(f"[向量库时间] 结果排序完成: {sort_time:.3f}秒")

            # 总搜索时间
            total_search_time = time.time() - search_start
            print(f"[向量库时间] 向量搜索总耗时: {total_search_time:.3f}秒")
            print(f"[向量库时间] 返回结果数: {len(limited_results)}")

            return limited_results
        except Exception as e:
            print(f"Error searching vectors: {e}")
            total_time = time.time() - search_start
            print(f"[向量库时间] 搜索失败，总耗时: {total_time:.3f}秒")
            return []

    async def migrate_collection_to_json_metadata(self, old_collection: str, new_collection: str = None):
        """
        将现有集合的字符串metadata迁移到新的JSON格式集合

        Args:
            old_collection: 现有集合名称
            new_collection: 新集合名称，如果为None则使用 old_collection + "_json"
        """
        if new_collection is None:
            new_collection = f"{old_collection}_json"

        print(f"[迁移] 开始将集合 '{old_collection}' 迁移到 '{new_collection}'")

        try:
            # 确保连接有效
            if self.client is None:
                await self.connect()

            # 检查源集合是否存在
            if old_collection not in self.collections_cache:
                await self._refresh_collections()
                if old_collection not in self.collections_cache:
                    raise ValueError(f"源集合 '{old_collection}' 不存在")

            # 查询所有数据
            print(f"[迁移] 正在查询集合 '{old_collection}' 的所有数据...")

            # 分批查询所有数据，避免一次性查询过多数据
            all_query_results = []
            offset = 0
            batch_size = 1000  # 每批查询1000条记录

            while True:
                # 使用空filter时必须指定limit参数
                batch_results = self.client.query(
                    collection_name=old_collection,
                    filter="",  # 查询所有数据
                    output_fields=["*"],
                    offset=offset,
                    limit=batch_size
                )

                if not batch_results:
                    break

                all_query_results.extend(batch_results)
                print(f"[迁移] 已查询 {len(all_query_results)} 条记录...")

                # 如果返回的记录数少于batch_size，说明已经查询完所有数据
                if len(batch_results) < batch_size:
                    break

                offset += batch_size

            query_results = all_query_results

            if not query_results:
                print(f"[迁移] 集合 '{old_collection}' 中没有数据")
                return

            print(f"[迁移] 找到 {len(query_results)} 条记录")

            # 准备迁移数据
            migrated_records = []
            for record in query_results:
                # 获取向量维度（从第一条记录）
                if 'vector' in record and len(migrated_records) == 0:
                    dim = len(record['vector'])
                    # 创建新集合
                    await self.create_collection(new_collection, dim)

                # 处理metadata字段
                metadata = record.get('metadata', '{}')
                if isinstance(metadata, str):
                    try:
                        import json
                        metadata_obj = json.loads(metadata)
                    except:
                        metadata_obj = {}
                else:
                    metadata_obj = metadata if isinstance(metadata, dict) else {}

                # 构建新记录
                new_record = {
                    'vector': record.get('vector', []),
                    'content': record.get('content', ''),
                    'metadata': metadata_obj
                }

                # 添加其他动态字段
                for key, value in record.items():
                    if key not in ['id', 'vector', 'content', 'metadata', 'collection_name']:
                        new_record[key] = value

                migrated_records.append(new_record)

            # 批量插入到新集合
            if migrated_records:
                result = await self.insert_vectors(new_collection, migrated_records)
                print(f"[迁移] 成功迁移 {result} 条记录到集合 '{new_collection}'")
                print(f"[迁移] 迁移完成！新集合使用JSON格式的metadata字段")
                return new_collection

        except Exception as e:
            print(f"[迁移错误] 迁移失败: {e}")
            raise

    async def upsert_vectors(self, collection: str, records: List[Dict[str, Any]]):
        """更新或插入向量记录

        Args:
            collection: 集合名称
            records: 要更新或插入的记录列表，必须包含主键字段
        """
        if not records:
            print(f"No records to upsert into collection '{collection}'")
            return 0

        # 确保集合存在
        if collection not in self.collections_cache:
            if 'vector' in records[0]:
                dim = len(records[0]['vector'])
                await self.create_collection(collection, dim)
            else:
                raise ValueError(f"Collection {collection} does not exist and no vectors provided")

        # 使用client方式执行upsert操作
        try:
            # 准备数据，格式与insert_vectors相同
            entities = []
            for r in records:
                # 处理metadata字段，支持JSON对象和字符串格式
                metadata_obj = r.get('metadata', {})

                # 向后兼容：如果传入的是字符串，尝试解析为JSON
                if isinstance(metadata_obj, str):
                    try:
                        import json
                        metadata_obj = json.loads(metadata_obj)
                        print(f"[兼容性] 成功解析字符串格式的metadata")
                    except Exception as e:
                        print(f"[兼容性警告] 无法解析metadata字符串，使用空字典: {e}")
                        metadata_obj = {}
                elif not isinstance(metadata_obj, dict):
                    print(f"[警告] metadata不是字典或字符串类型，已重置为空字典")
                    metadata_obj = {}

                # 对于upsert操作，需要包含主键字段
                entity = {
                    "vector": r['vector'],
                    "content": r.get('content', ''),
                    "collection_name": collection,
                    "metadata": metadata_obj  # 直接使用JSON对象
                }

                # 如果记录中包含id字段，则添加到entity中（用于upsert）
                if 'id' in r:
                    entity['id'] = r['id']

                # 添加所有额外字段作为独立列
                for key, value in r.items():
                    # 跳过已处理的标准字段
                    if key in ['vector', 'content', 'metadata', 'id']:
                        continue

                    # 如果是自定义字段，直接添加到entity中
                    entity[key] = value
                    print(f"[动态字段] 添加字段 '{key}' = {value} 作为独立列")

                entities.append(entity)

            # 执行upsert操作
            result = self.client.upsert(
                collection_name=collection,
                data=entities
            )
            upsert_count = result.get('upsert_count', len(entities)) if isinstance(result, dict) else len(entities)
            print(f"Upserted {upsert_count} vectors into collection '{collection}' using JSON metadata")
            return upsert_count
        except Exception as e:
            print(f"Error upserting vectors: {e}")
            raise

    async def delete_vectors(self, collection: str, ids: List[Any] = None, filter_expr: str = ""):
        """删除向量记录

        Args:
            collection: 集合名称
            ids: 要删除的主键ID列表，如果为None则使用filter_expr
            filter_expr: 删除条件表达式，例如 "color in ['red', 'blue']"
        """
        # 确保连接有效
        if self.client is None:
            print("[向量库] 客户端未初始化，尝试连接...")
            await self.connect()

        # 确保集合存在
        if collection not in self.collections_cache:
            await self._refresh_collections()
            if collection not in self.collections_cache:
                raise ValueError(f"Collection '{collection}' does not exist")

        try:
            if ids is not None and len(ids) > 0:
                # 按主键ID删除
                result = self.client.delete(
                    collection_name=collection,
                    ids=ids
                )
                delete_count = result.get('delete_count', len(ids)) if isinstance(result, dict) else len(ids)
                print(f"Deleted {delete_count} entities by IDs from collection '{collection}'")
                return delete_count
            elif filter_expr:
                # 按条件删除
                result = self.client.delete(
                    collection_name=collection,
                    filter=filter_expr
                )
                delete_count = result.get('delete_count', 0) if isinstance(result, dict) else 0
                print(f"Deleted {delete_count} entities by filter '{filter_expr}' from collection '{collection}'")
                return delete_count
            else:
                raise ValueError("Either 'ids' or 'filter_expr' must be provided for deletion")

        except Exception as e:
            print(f"Error deleting vectors: {e}")
            raise

    async def flush_collection(self, collection: str):
        """刷新集合数据，确保数据持久化"""
        # 确保连接有效
        if self.client is None:
            print("[向量库] 客户端未初始化，尝试连接...")
            await self.connect()

        try:
            # 调用flush方法刷新数据
            self.client.flush(collection_name=collection)
            print(f"✓ 成功刷新集合 '{collection}' 的数据")
        except Exception as e:
            print(f"⚠ 刷新集合 '{collection}' 失败: {e}")
            # 不抛出异常，因为flush失败不应该中断程序